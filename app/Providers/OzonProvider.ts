import { IOzonProvider } from 'App/Interfaces/ozon/OzonInterfaces'
import { 
  OzonProduct, 
  OzonStock, 
  OzonPrice, 
  OzonProductInfo,
  OzonSyncAction,
  OzonSyncStatus
} from 'App/Interfaces/ozon/OzonTypes'
import { OzonClient } from 'App/Clients/OzonClient'
import { OzonImageService } from 'App/Services/OzonImageService'
import { ozonConfig } from 'App/Services/OzonConfig'
import { $prisma } from 'App/Services/Prisma'

export class OzonProvider implements IOzonProvider {
  private ozonClient: OzonClient
  private imageService: OzonImageService

  constructor() {
    this.ozonClient = new OzonClient()
    this.imageService = new OzonImageService()
  }

  /**
   * Синхронизация товаров по массиву ID
   */
  async syncProducts(productIds: number[]): Promise<{
    success: number[]
    failed: { productId: number, error: string }[]
    total: number
  }> {
    const success: number[] = []
    const failed: { productId: number, error: string }[] = []

    console.log(`Starting sync for ${productIds.length} products`)

    // Проверяем, включена ли синхронизация
    const isEnabled = await ozonConfig.isEnabled()
    if (!isEnabled) {
      throw new Error('Ozon synchronization is disabled')
    }

    // Получаем товары из БД
    const products = await $prisma.products.findMany({
      where: {
        prod_id: {
          in: productIds
        }
      }
    })

    if (products.length === 0) {
      throw new Error('No products found')
    }

    // Синхронизируем каждый товар
    for (const product of products) {
      try {
        console.log(`Syncing product ${product.prod_id} (${product.prod_sku})`)

        // Маппим товар в формат Ozon
        const ozonProduct = await this.mapProductToOzon(product)

        // Проверяем, существует ли товар в Ozon
        const existingProduct = await this.getProductInfo(product.prod_sku!)
        
        if (existingProduct) {
          // Обновляем существующий товар
          await this.updateProduct(product.prod_sku!, ozonProduct)
          await this.logOperation(product.prod_id, OzonSyncAction.UPDATE, OzonSyncStatus.SUCCESS)
        } else {
          // Создаем новый товар
          await this.createProduct(ozonProduct)
          await this.logOperation(product.prod_id, OzonSyncAction.CREATE, OzonSyncStatus.SUCCESS)
        }

        // Обновляем остатки
        await this.updateStocks([{
          offer_id: product.prod_sku!,
          stock: product.prod_count,
          warehouse_id: (await ozonConfig.getSetting('ozon_warehouse_id'))
        }])

        success.push(product.prod_id)

      } catch (error) {
        console.error(`Error syncing product ${product.prod_id}:`, error)
        failed.push({
          productId: product.prod_id,
          error: error.message || 'Unknown error'
        })
        
        await this.logOperation(
          product.prod_id, 
          OzonSyncAction.SYNC, 
          OzonSyncStatus.ERROR, 
          error.message
        )
      }
    }

    console.log(`Sync completed: ${success.length} success, ${failed.length} failed`)

    return {
      success,
      failed,
      total: productIds.length
    }
  }

  /**
   * Создание товара в Ozon
   */
  async createProduct(productData: OzonProduct): Promise<{
    success: boolean
    productId?: number
    error?: string
  }> {
    try {
      const response = await this.ozonClient.importProducts({
        items: [productData]
      })

      if (response.result) {
        return {
          success: true,
          productId: response.result.task_id
        }
      }

      return {
        success: false,
        error: 'No result in response'
      }

    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to create product'
      }
    }
  }

  /**
   * Обновление товара в Ozon
   */
  async updateProduct(offerId: string, productData: Partial<OzonProduct>): Promise<{
    success: boolean
    error?: string
  }> {
    try {
      // Для обновления используем тот же метод импорта
      const fullProductData = {
        offer_id: offerId,
        ...productData
      } as OzonProduct

      const response = await this.ozonClient.importProducts({
        items: [fullProductData]
      })

      return {
        success: !!response.result
      }

    } catch (error) {
      return {
        success: false,
        error: error.message || 'Failed to update product'
      }
    }
  }

  /**
   * Обновление остатков
   */
  async updateStocks(stocks: OzonStock[]): Promise<{
    success: boolean
    updated: number
    failed: { offerId: string, error: string }[]
  }> {
    try {
      const response = await this.ozonClient.updateStocks({ stocks })
      
      return {
        success: !!response.result,
        updated: stocks.length,
        failed: []
      }

    } catch (error) {
      return {
        success: false,
        updated: 0,
        failed: stocks.map(stock => ({
          offerId: stock.offer_id,
          error: error.message || 'Failed to update stock'
        }))
      }
    }
  }

  /**
   * Обновление цен
   */
  async updatePrices(prices: OzonPrice[]): Promise<{
    success: boolean
    updated: number
    failed: { offerId: string, error: string }[]
  }> {
    try {
      const response = await this.ozonClient.updatePrices({ prices })
      
      return {
        success: !!response.result,
        updated: prices.length,
        failed: []
      }

    } catch (error) {
      return {
        success: false,
        updated: 0,
        failed: prices.map(price => ({
          offerId: price.offer_id,
          error: error.message || 'Failed to update price'
        }))
      }
    }
  }

  /**
   * Получение информации о товаре
   */
  async getProductInfo(offerId: string): Promise<OzonProductInfo | null> {
    try {
      const response = await this.ozonClient.getProductInfo({
        offer_id: [offerId]
      })

      if (response.result?.items && response.result.items.length > 0) {
        return response.result.items[0]
      }

      return null

    } catch (error) {
      console.error(`Error getting product info for ${offerId}:`, error)
      return null
    }
  }

  /**
   * Получение статуса товара
   */
  async getProductStatus(offerId: string): Promise<{
    status: string
    errors: string[]
    isActive: boolean
  }> {
    try {
      const productInfo = await this.getProductInfo(offerId)
      
      if (!productInfo) {
        return {
          status: 'not_found',
          errors: ['Product not found'],
          isActive: false
        }
      }

      return {
        status: productInfo.status?.state || 'unknown',
        errors: productInfo.errors?.map(e => e.message) || [],
        isActive: productInfo.visible
      }

    } catch (error) {
      return {
        status: 'error',
        errors: [error.message || 'Failed to get status'],
        isActive: false
      }
    }
  }

  /**
   * Загрузка изображений товара
   */
  async uploadProductImages(productId: number): Promise<{
    success: boolean
    uploadedImages: string[]
    failed: { fileName: string, error: string }[]
  }> {
    try {
      // Получаем товар из БД
      const product = await $prisma.products.findUnique({
        where: { prod_id: productId }
      })

      if (!product) {
        throw new Error('Product not found')
      }

      // Подготавливаем изображения
      const imageResult = await this.imageService.prepareProductImages(product)
      
      if (!imageResult.success) {
        return {
          success: false,
          uploadedImages: [],
          failed: imageResult.errors.map(error => ({ fileName: '', error }))
        }
      }

      await this.logOperation(productId, OzonSyncAction.UPLOAD_IMAGE, OzonSyncStatus.SUCCESS)

      return {
        success: true,
        uploadedImages: imageResult.images,
        failed: []
      }

    } catch (error) {
      await this.logOperation(productId, OzonSyncAction.UPLOAD_IMAGE, OzonSyncStatus.ERROR, error.message)
      
      return {
        success: false,
        uploadedImages: [],
        failed: [{ fileName: '', error: error.message || 'Failed to upload images' }]
      }
    }
  }

  /**
   * Маппинг товара из БД в формат Ozon
   */
  private async mapProductToOzon(product: any): Promise<OzonProduct> {
    const settings = await ozonConfig.getSettings()
    
    // Подготавливаем изображения
    const imageResult = await this.imageService.prepareProductImages(product)
    
    return {
      offer_id: `product_${product.prod_id}`,
      name: this.truncateString(`${product.prod_purpose} ${product.prod_sku}/${product.prod_sku}, размер: ${product.prod_size}, тип: ${product.prod_type}`, 500),
      description: this.truncateString(`${product.prod_uses}, ${product.prod_note}`, 4000),
      category_id: parseInt(product.prod_cat) || settings.ozon_default_category_id,
      price: String(Number(product.prod_price) + 500),
      currency_code: 'RUB',
      vat: '0', // НДС 20%
      weight: this.parseWeight(product.prod_weight),
      weight_unit: 'g',
      images: imageResult.images || [],
      primary_image: imageResult.primaryImage,
      attributes: await this.buildProductAttributes(product)
    }
  }

  /**
   * Построение атрибутов товара
   */
  private async buildProductAttributes(product: any): Promise<any[]> {
    const attributes = []

    // Бренд
    if (product.prod_manuf) {
      attributes.push({
        id: 85, // ID атрибута "Бренд" в Ozon
        values: [{ value: 'SNF' }]
      })
    }

    // Модель
    if (product.prod_model) {
      attributes.push({
        id: 9048, // ID атрибута "Модель"
        values: [{ value: String(product.prod_model) }]
      })
    }

    // Материал
    if (product.prod_material) {
      attributes.push({
        id: 10096, // ID атрибута "Материал"
        values: [{ value: String(product.prod_material) }]
      })
    }

    return attributes
  }

  /**
   * Логирование операций
   */
  private async logOperation(
    productId: number, 
    action: OzonSyncAction, 
    status: OzonSyncStatus, 
    error?: string
  ): Promise<void> {
    try {
      // Здесь можно добавить запись в таблицу логов
      console.log(`Ozon operation log: Product ${productId}, Action: ${action}, Status: ${status}`, error ? `Error: ${error}` : '')
    } catch (logError) {
      console.error('Failed to log operation:', logError)
    }
  }

  /**
   * Утилиты
   */
  private truncateString(str: string, maxLength: number): string {
    if (str.length <= maxLength) return str
    return str.substring(0, maxLength - 3) + '...'
  }

  private parseWeight(weight: string | number): number {
    if (typeof weight === 'number') return weight
    if (typeof weight === 'string') {
      const parsed = parseFloat(weight.replace(/[^\d.,]/g, '').replace(',', '.'))
      return isNaN(parsed) ? 0 : parsed
    }
    return 0
  }
}

// Экспортируем singleton instance
export const ozonProvider = new OzonProvider()

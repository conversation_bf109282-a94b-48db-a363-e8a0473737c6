import loadSettings from 'App/Helpers/loadSettings'
import { $prisma } from 'App/Services/Prisma'
import { IOzonConfig } from 'App/Interfaces/ozon/OzonInterfaces'
import { OzonSettings } from 'App/Interfaces/ozon/OzonTypes'

export class OzonConfig implements IOzonConfig {
  private settingsCache: OzonSettings | null = null
  private cacheExpiry: number = 0
  private readonly CACHE_TTL = 60000 // 1 минута

  // Список всех настроек Ozon
  private readonly OZON_SETTINGS_KEYS: (keyof OzonSettings)[] = [
    'ozon_api_key',
    'ozon_client_id',
    'ozon_api_url',
    'ozon_warehouse_id',
    'ozon_sync_enabled',
    'ozon_auto_sync_interval',
    'ozon_default_category_id',
    'ozon_image_upload_enabled',
    'ozon_image_base_url',
    'ozon_max_images_per_product'
  ]

  // Значения по умолчанию
  private readonly DEFAULT_SETTINGS: Partial<OzonSettings> = {
    ozon_api_url: 'https://api-seller.ozon.ru',
    ozon_warehouse_id: 0,
    ozon_sync_enabled: false,
    ozon_auto_sync_interval: 60,
    ozon_default_category_id: 0,
    ozon_image_upload_enabled: false,
    ozon_image_base_url: '',
    ozon_max_images_per_product: 10
  }

  /**
   * Получение всех настроек Ozon
   */
  async getSettings(): Promise<OzonSettings> {
    // Проверяем кэш
    if (this.settingsCache && Date.now() < this.cacheExpiry) {
      return this.settingsCache
    }

    try {
      // Загружаем настройки через loadSettings
      const settings = await loadSettings(this.OZON_SETTINGS_KEYS)
      
      // Объединяем с значениями по умолчанию
      this.settingsCache = {
        ...this.DEFAULT_SETTINGS,
        ...settings
      } as OzonSettings

      // Устанавливаем время истечения кэша
      this.cacheExpiry = Date.now() + this.CACHE_TTL

      return this.settingsCache

    } catch (error) {
      console.error('Error loading Ozon settings:', error)
      
      // Возвращаем значения по умолчанию в случае ошибки
      return this.DEFAULT_SETTINGS as OzonSettings
    }
  }

  /**
   * Получение конкретной настройки
   */
  async getSetting<T = any>(key: keyof OzonSettings): Promise<T> {
    const settings = await this.getSettings()
    return settings[key] as T
  }

  /**
   * Обновление настройки
   */
  async updateSetting(key: keyof OzonSettings, value: any): Promise<void> {
    try {
      // Определяем тип значения для правильного сохранения
      const isJson = typeof value === 'object' && value !== null

      // Обновляем в базе данных
      await $prisma.settings.upsert({
        where: { s_key: key as string },
        update: {
          s_value: isJson ? JSON.stringify(value) : String(value),
          json: isJson
        },
        create: {
          s_key: key as string,
          s_value: isJson ? JSON.stringify(value) : String(value),
          json: isJson,
          syst: false
        }
      })

      // Сбрасываем кэш
      this.clearCache()

    } catch (error) {
      console.error(`Error updating Ozon setting ${key}:`, error)
      throw new Error(`Failed to update setting ${key}: ${error.message}`)
    }
  }

  /**
   * Массовое обновление настроек
   */
  async updateSettings(settings: Partial<OzonSettings>): Promise<void> {
    const updatePromises = Object.entries(settings).map(([key, value]) =>
      this.updateSetting(key as keyof OzonSettings, value)
    )

    await Promise.all(updatePromises)
  }

  /**
   * Проверка, включена ли интеграция с Ozon
   */
  async isEnabled(): Promise<boolean> {
    return await this.getSetting<boolean>('ozon_sync_enabled')
  }

  /**
   * Проверка корректности настроек API
   */
  async validateApiSettings(): Promise<{
    isValid: boolean
    errors: string[]
  }> {
    const settings = await this.getSettings()
    const errors: string[] = []

    if (!settings.ozon_api_key) {
      errors.push('API ключ не установлен')
    }

    if (!settings.ozon_client_id) {
      errors.push('Client ID не установлен')
    }

    if (!settings.ozon_api_url) {
      errors.push('URL API не установлен')
    }

    if (!settings.ozon_warehouse_id || settings.ozon_warehouse_id <= 0) {
      errors.push('ID склада не установлен или некорректен')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Получение настроек для изображений
   */
  async getImageSettings(): Promise<{
    enabled: boolean
    baseUrl: string
    maxImages: number
  }> {
    const settings = await this.getSettings()
    
    return {
      enabled: settings.ozon_image_upload_enabled,
      baseUrl: settings.ozon_image_base_url,
      maxImages: settings.ozon_max_images_per_product
    }
  }

  /**
   * Получение настроек синхронизации
   */
  async getSyncSettings(): Promise<{
    enabled: boolean
    interval: number
    defaultCategoryId: number
  }> {
    const settings = await this.getSettings()
    
    return {
      enabled: settings.ozon_sync_enabled,
      interval: settings.ozon_auto_sync_interval,
      defaultCategoryId: settings.ozon_default_category_id
    }
  }

  /**
   * Сброс кэша настроек
   */
  clearCache(): void {
    this.settingsCache = null
    this.cacheExpiry = 0
  }

  /**
   * Инициализация настроек по умолчанию
   */
  async initializeDefaultSettings(): Promise<void> {
    const existingSettings = await $prisma.settings.findMany({
      where: {
        s_key: {
          in: this.OZON_SETTINGS_KEYS as string[]
        }
      }
    })

    const existingKeys = existingSettings.map(s => s.s_key)
    const missingSettings = this.OZON_SETTINGS_KEYS.filter(key => !existingKeys.includes(key as string))

    if (missingSettings.length > 0) {
      const defaultEntries = missingSettings.map(key => {
        const value = this.DEFAULT_SETTINGS[key]
        const isJson = typeof value === 'object' && value !== null

        return {
          s_key: key as string,
          s_value: isJson ? JSON.stringify(value) : String(value),
          json: isJson,
          syst: false
        }
      })

      await $prisma.settings.createMany({
        data: defaultEntries,
        skipDuplicates: true
      })

      console.log(`Initialized ${missingSettings.length} default Ozon settings`)
    }
  }

  /**
   * Получение статистики использования настроек
   */
  async getSettingsStats(): Promise<{
    totalSettings: number
    configuredSettings: number
    missingSettings: string[]
  }> {
    const settings = await this.getSettings()
    const configuredSettings = Object.entries(settings).filter(([key, value]) => {
      if (typeof value === 'string') return value.trim() !== ''
      if (typeof value === 'number') return value > 0
      if (typeof value === 'boolean') return true
      return value != null
    })

    const missingSettings = this.OZON_SETTINGS_KEYS.filter(key => {
      const value = settings[key]
      if (typeof value === 'string') return value.trim() === ''
      if (typeof value === 'number') return value <= 0
      return value == null
    })

    return {
      totalSettings: this.OZON_SETTINGS_KEYS.length,
      configuredSettings: configuredSettings.length,
      missingSettings: missingSettings as string[]
    }
  }
}

// Экспортируем singleton instance
export const ozonConfig = new OzonConfig()

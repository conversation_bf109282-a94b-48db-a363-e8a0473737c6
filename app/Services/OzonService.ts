import { IOzonService, IOzonValidator, IOzonLogger } from 'App/Interfaces/ozon/OzonInterfaces'
import { 
  OzonProduct, 
  OzonProductInfo, 
  OzonSettings,
  OzonSyncLog,
  OzonSyncAction,
  OzonSyncStatus,
  OzonStock,
  OzonPrice
} from 'App/Interfaces/ozon/OzonTypes'
import { ozonProvider } from 'App/Providers/OzonProvider'
import { OzonImageService } from 'App/Services/OzonImageService'
import { ozonConfig } from 'App/Services/OzonConfig'
import { $prisma } from 'App/Services/Prisma'

// Валидатор для Ozon данных
class OzonValidator implements IOzonValidator {
  async validateProduct(product: OzonProduct): Promise<{
    isValid: boolean
    errors: string[]
    warnings: string[]
  }> {
    const errors: string[] = []
    const warnings: string[] = []

    // Обязательные поля
    if (!product.offer_id) {
      errors.push('offer_id is required')
    }

    if (!product.name || product.name.trim().length === 0) {
      errors.push('name is required')
    }

    if (!product.category_id || product.category_id <= 0) {
      errors.push('valid category_id is required')
    }

    if (!product.price || parseFloat(product.price) <= 0) {
      errors.push('valid price is required')
    }

    // Проверка длины полей
    if (product.name && product.name.length > 500) {
      errors.push('name is too long (max 500 characters)')
    }

    if (product.description && product.description.length > 4000) {
      errors.push('description is too long (max 4000 characters)')
    }

    // Предупреждения
    if (product.name && product.name.length < 10) {
      warnings.push('name is very short (recommended min 10 characters)')
    }

    if (!product.description || product.description.length < 50) {
      warnings.push('description is too short (recommended min 50 characters)')
    }

    if (!product.images || product.images.length === 0) {
      warnings.push('no images provided')
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  async validateStock(stock: OzonStock): Promise<{
    isValid: boolean
    errors: string[]
  }> {
    const errors: string[] = []

    if (!stock.offer_id) {
      errors.push('offer_id is required')
    }

    if (stock.stock < 0) {
      errors.push('stock cannot be negative')
    }

    if (!stock.warehouse_id || stock.warehouse_id <= 0) {
      errors.push('valid warehouse_id is required')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  async validatePrice(price: OzonPrice): Promise<{
    isValid: boolean
    errors: string[]
  }> {
    const errors: string[] = []

    if (!price.offer_id) {
      errors.push('offer_id is required')
    }

    if (!price.price || parseFloat(price.price) <= 0) {
      errors.push('valid price is required')
    }

    if (!price.currency_code) {
      errors.push('currency_code is required')
    }

    if (price.old_price && parseFloat(price.old_price) <= parseFloat(price.price)) {
      errors.push('old_price should be greater than current price')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  async validateSettings(settings: Partial<OzonSettings>): Promise<{
    isValid: boolean
    errors: string[]
  }> {
    const errors: string[] = []

    if (settings.ozon_api_key && settings.ozon_api_key.length < 10) {
      errors.push('API key is too short')
    }

    if (settings.ozon_client_id && settings.ozon_client_id.length < 5) {
      errors.push('Client ID is too short')
    }

    if (settings.ozon_warehouse_id && settings.ozon_warehouse_id <= 0) {
      errors.push('Warehouse ID must be positive')
    }

    if (settings.ozon_auto_sync_interval && settings.ozon_auto_sync_interval < 1) {
      errors.push('Sync interval must be at least 1 minute')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }
}

// Логгер для Ozon операций
class OzonLogger implements IOzonLogger {
  async log(
    productId: number, 
    action: string, 
    status: string, 
    error?: string, 
    additionalData?: any
  ): Promise<void> {
    try {
      // Здесь можно создать таблицу ozon_sync_logs в БД
      // Пока логируем в консоль
      const logEntry = {
        product_id: productId,
        action,
        status,
        error_message: error,
        additional_data: additionalData ? JSON.stringify(additionalData) : null,
        created_at: new Date()
      }

      console.log('Ozon Sync Log:', logEntry)

      // TODO: Сохранить в БД когда будет создана таблица
      // await $prisma.ozon_sync_logs.create({ data: logEntry })

    } catch (logError) {
      console.error('Failed to log Ozon operation:', logError)
    }
  }

  async getHistory(productId?: number, limit = 100, offset = 0): Promise<OzonSyncLog[]> {
    // TODO: Реализовать когда будет создана таблица
    return []
  }

  async clearOldLogs(daysToKeep = 30): Promise<number> {
    // TODO: Реализовать когда будет создана таблица
    return 0
  }
}

export class OzonService implements IOzonService {
  private validator: IOzonValidator
  private logger: IOzonLogger
  private imageService: OzonImageService

  constructor() {
    this.validator = new OzonValidator()
    this.logger = new OzonLogger()
    this.imageService = new OzonImageService()
  }

  /**
   * Маппинг товара из БД в формат Ozon
   */
  async mapProductToOzon(product: any): Promise<OzonProduct> {
    const settings = await ozonConfig.getSettings()
    
    // Подготавливаем изображения
    const imageResult = await this.imageService.prepareProductImages(product)
    
    // Базовые атрибуты
    const attributes = []

    // Бренд
    if (product.prod_manuf) {
      attributes.push({
        id: 85, // ID атрибута "Бренд"
        values: [{ value: String(product.prod_manuf) }]
      })
    }

    // Модель
    if (product.prod_model) {
      attributes.push({
        id: 9048, // ID атрибута "Модель"
        values: [{ value: String(product.prod_model) }]
      })
    }

    // Материал
    if (product.prod_material) {
      attributes.push({
        id: 10096, // ID атрибута "Материал"
        values: [{ value: String(product.prod_material) }]
      })
    }

    // Размеры
    if (product.size_in || product.size_out || product.size_h) {
      const dimensions = []
      if (product.size_in) dimensions.push(`Внутренний: ${product.size_in}`)
      if (product.size_out) dimensions.push(`Внешний: ${product.size_out}`)
      if (product.size_h) dimensions.push(`Высота: ${product.size_h}`)
      
      if (dimensions.length > 0) {
        attributes.push({
          id: 9461, // ID атрибута "Размеры"
          values: [{ value: dimensions.join(', ') }]
        })
      }
    }

    const ozonProduct: OzonProduct = {
      offer_id: product.prod_sku || `product_${product.prod_id}`,
      name: this.truncateString(product.prod_note || `Товар ${product.prod_sku}`, 500),
      description: this.prepareDescription(product),
      category_id: this.parseCategoryId(product.prod_cat) || settings.ozon_default_category_id,
      price: String(product.prod_price || 0),
      old_price: product.prod_discount > 0 ? String((product.prod_price || 0) * (1 + product.prod_discount / 100)) : undefined,
      currency_code: 'RUB',
      vat: '0.20', // НДС 20%
      height: product.size_h || undefined,
      width: product.size_out || undefined,
      depth: product.size_in || undefined,
      dimension_unit: 'mm',
      weight: this.parseWeight(product.prod_weight),
      weight_unit: 'g',
      images: imageResult.images || [],
      primary_image: imageResult.primaryImage,
      attributes
    }

    return ozonProduct
  }

  /**
   * Маппинг данных из Ozon в формат БД
   */
  async mapOzonToProduct(ozonData: OzonProductInfo): Promise<any> {
    return {
      prod_sku: ozonData.offer_id,
      prod_note: ozonData.name,
      prod_price: parseFloat(ozonData.price) || 0,
      prod_count: ozonData.stocks?.present || 0,
      // Другие поля можно добавить по необходимости
    }
  }

  /**
   * Валидация данных товара
   */
  async validateProductData(data: OzonProduct): Promise<{
    isValid: boolean
    errors: string[]
  }> {
    const result = await this.validator.validateProduct(data)
    return {
      isValid: result.isValid,
      errors: result.errors
    }
  }

  /**
   * Загрузка изображений товара
   */
  async uploadProductImages(product: any): Promise<{
    success: boolean
    imageUrls: string[]
    errors: string[]
  }> {
    try {
      const result = await this.imageService.prepareProductImages(product)
      
      await this.logger.log(
        product.prod_id, 
        OzonSyncAction.UPLOAD_IMAGE, 
        result.success ? OzonSyncStatus.SUCCESS : OzonSyncStatus.ERROR,
        result.errors.join(', ')
      )

      return {
        success: result.success,
        imageUrls: result.images,
        errors: result.errors
      }

    } catch (error) {
      await this.logger.log(
        product.prod_id, 
        OzonSyncAction.UPLOAD_IMAGE, 
        OzonSyncStatus.ERROR, 
        error.message
      )

      return {
        success: false,
        imageUrls: [],
        errors: [error.message || 'Failed to upload images']
      }
    }
  }

  /**
   * Получение URL изображений
   */
  async getImageUrls(product: any): Promise<string[]> {
    const localImages = await this.imageService.getLocalImages(product)
    return localImages.imagePaths.map(path => 
      this.imageService.getImageUrl(path.split('/').pop()!, 'rti')
    )
  }

  /**
   * Логирование операций синхронизации
   */
  async logSyncOperation(
    productId: number, 
    action: string, 
    status: string, 
    error?: string
  ): Promise<void> {
    await this.logger.log(productId, action, status, error)
  }

  /**
   * Получение истории синхронизации
   */
  async getSyncHistory(productId?: number, limit = 100): Promise<OzonSyncLog[]> {
    return await this.logger.getHistory(productId, limit)
  }

  /**
   * Получение настроек
   */
  async getSettings(): Promise<OzonSettings> {
    return await ozonConfig.getSettings()
  }

  /**
   * Обновление настроек
   */
  async updateSettings(settings: Partial<OzonSettings>): Promise<void> {
    const validation = await this.validator.validateSettings(settings)

    if (!validation.isValid) {
      throw new Error(`Invalid settings: ${validation.errors.join(', ')}`)
    }

    await ozonConfig.updateSettings(settings)
  }

  /**
   * Синхронизация товаров (для tRPC роутера)
   */
  async syncProducts(productIds: number[], batchSize = 10, uploadImages = true): Promise<{
    success: boolean
    total: number
    synced: number
    failed: number
    results: any
    message: string
  }> {
    // Проверяем, включена ли синхронизация
    const isEnabled = await ozonConfig.isEnabled()
    if (!isEnabled) {
      throw new Error('Ozon synchronization is disabled. Please enable it in settings.')
    }

    // Синхронизируем товары
    const result = await ozonProvider.syncProducts(productIds)

    // Логируем результат
    await this.logger.log(
      0, // Общий лог для всех товаров
      'batch_sync',
      result.failed.length === 0 ? 'success' : 'partial_success',
      result.failed.length > 0 ? `${result.failed.length} products failed` : undefined
    )

    return {
      success: result.failed.length === 0,
      total: result.total,
      synced: result.success.length,
      failed: result.failed.length,
      results: result,
      message: `Synced ${result.success.length} of ${result.total} products`
    }
  }

  /**
   * Получение статуса товара (для tRPC роутера)
   */
  async getProductStatus(productId?: number, offerId?: string): Promise<{
    offerId: string
    status: string
    errors: string[]
    isActive: boolean
    productInfo: any
  }> {
    let finalOfferId = offerId

    // Если передан productId, получаем offerId из БД
    if (productId && !finalOfferId) {
      const product = await $prisma.products.findUnique({
        where: { prod_id: productId },
        select: { prod_sku: true }
      })

      if (!product?.prod_sku) {
        throw new Error('Product not found or has no SKU')
      }

      finalOfferId = product.prod_sku
    }

    if (!finalOfferId) {
      throw new Error('No offer ID available')
    }

    const status = await ozonProvider.getProductStatus(finalOfferId)
    const productInfo = await ozonProvider.getProductInfo(finalOfferId)

    return {
      offerId: finalOfferId,
      status: status.status,
      errors: status.errors,
      isActive: status.isActive,
      productInfo: productInfo ? {
        id: productInfo.id,
        name: productInfo.name,
        price: productInfo.price,
        stocks: productInfo.stocks,
        visible: productInfo.visible,
        state: productInfo.state
      } : null
    }
  }

  /**
   * Обновление остатков товара (для tRPC роутера)
   */
  async updateProductStock(productId: number, stock: number, offerId?: string): Promise<{
    success: boolean
    offerId: string
    stock: number
    message: string
  }> {
    let finalOfferId = offerId

    // Получаем offerId из БД если не передан
    if (!finalOfferId) {
      const product = await $prisma.products.findUnique({
        where: { prod_id: productId },
        select: { prod_sku: true }
      })

      if (!product?.prod_sku) {
        throw new Error('Product not found or has no SKU')
      }

      finalOfferId = product.prod_sku
    }

    const warehouseId = await ozonConfig.getSetting<number>('ozon_warehouse_id')

    const result = await ozonProvider.updateStocks([{
      offer_id: finalOfferId,
      stock: stock,
      warehouse_id: warehouseId
    }])

    await this.logger.log(
      productId,
      'update_stock',
      result.success ? 'success' : 'error',
      result.failed.length > 0 ? result.failed[0].error : undefined
    )

    return {
      success: result.success,
      offerId: finalOfferId,
      stock: stock,
      message: result.success ? 'Stock updated successfully' : 'Failed to update stock'
    }
  }

  /**
   * Обновление цены товара (для tRPC роутера)
   */
    additionalPrice?: number
    async updateProductPrice(productId: number, price: number, oldPrice?: number, offerId?: string, additionalPrice = 500): Promise<{
    success: boolean
    offerId: string
    price: number
    oldPrice?: number
    message: string,
  }> {
    let finalOfferId = offerId

    // Получаем offerId из БД если не передан
    if (!finalOfferId) {
      const product = await $prisma.products.findUnique({
        where: { prod_id: productId },
        select: { prod_sku: true }
      })

      if (!product?.prod_sku) {
        throw new Error('Product not found or has no SKU')
      }

      finalOfferId = product.prod_sku
    }

    const result = await ozonProvider.updatePrices([
      {
        offer_id: finalOfferId,
        price: String(price + additionalPrice),
        old_price: oldPrice ? String(oldPrice) : undefined,
        currency_code: 'RUB'
      }
    ])

    await this.logger.log(
      productId,
      'update_price',
      result.success ? 'success' : 'error',
      result.failed.length > 0 ? result.failed[0].error : undefined
    )

    return {
      success: result.success,
      offerId: finalOfferId,
      price: price + additionalPrice,
      oldPrice: oldPrice,
      message: result.success ? 'Price updated successfully' : 'Failed to update price'
    }
  }

  /**
   * Загрузка изображений товара (для tRPC роутера)
   */
  async uploadProductImagesForRouter(productId: number): Promise<{
    success: boolean
    uploadedImages: string[]
    failed: { fileName: string, error: string }[]
    message: string
  }> {
    const result = await ozonProvider.uploadProductImages(productId)

    return {
      success: result.success,
      uploadedImages: result.uploadedImages,
      failed: result.failed,
      message: result.success
        ? `Uploaded ${result.uploadedImages.length} images`
        : `Failed to upload images: ${result.failed.map(f => f.error).join(', ')}`
    }
  }

  /**
   * Получение настроек Ozon (для tRPC роутера)
   */
  async getOzonSettingsForRouter(): Promise<{
    settings: OzonSettings
    validation: any
    stats: any
    isConfigured: boolean
    isEnabled: boolean
  }> {
    const settings = await this.getSettings()
    const validation = await ozonConfig.validateApiSettings()
    const stats = await ozonConfig.getSettingsStats()

    return {
      settings,
      validation,
      stats,
      isConfigured: validation.isValid,
      isEnabled: settings.ozon_sync_enabled
    }
  }

  /**
   * Обновление настроек Ozon (для tRPC роутера)
   */
  async updateOzonSettingsForRouter(settings: Partial<OzonSettings>): Promise<{
    success: boolean
    message: string
  }> {
    await this.updateSettings(settings)

    // Сбрасываем кэш настроек в клиенте
    const { OzonClient } = await import('App/Clients/OzonClient')
    const client = new OzonClient()
    client.resetSettings()

    return {
      success: true,
      message: 'Settings updated successfully'
    }
  }

  /**
   * Получение истории синхронизации (для tRPC роутера)
   */
  async getSyncHistoryForRouter(productId?: number, limit = 100, offset = 0): Promise<{
    history: OzonSyncLog[]
    total: number
    hasMore: boolean
  }> {
    const history = await this.getSyncHistory(productId, limit)

    return {
      history,
      total: history.length,
      hasMore: history.length === limit
    }
  }

  /**
   * Проверка подключения к Ozon API (для tRPC роутера)
   */
  async testConnection(): Promise<{
    success: boolean
    settings: any
    message: string
  }> {
    try {
      const { OzonClient } = await import('App/Clients/OzonClient')
      const client = new OzonClient()

      const isHealthy = await client.healthCheck()
      const settings = await ozonConfig.getSettings()

      return {
        success: isHealthy,
        settings: {
          hasApiKey: !!settings.ozon_api_key,
          hasClientId: !!settings.ozon_client_id,
          apiUrl: settings.ozon_api_url,
          warehouseId: settings.ozon_warehouse_id
        },
        message: isHealthy ? 'Connection successful' : 'Connection failed'
      }

    } catch (error) {
      return {
        success: false,
        settings: null,
        message: `Connection test failed: ${error.message}`
      }
    }
  }

  /**
   * Получение статистики синхронизации (для tRPC роутера)
   */
  async getSyncStats(): Promise<{
    totalProducts: number
    syncedProducts: number
    failedProducts: number
    lastSyncTime: Date | null
    averageSyncTime: number
    errorRate: number
    recentErrors: string[]
  }> {
    // Здесь можно добавить реальную статистику из БД
    return {
      totalProducts: 0,
      syncedProducts: 0,
      failedProducts: 0,
      lastSyncTime: null,
      averageSyncTime: 0,
      errorRate: 0,
      recentErrors: []
    }
  }

  // Утилиты
  private truncateString(str: string, maxLength: number): string {
    if (!str) return ''
    if (str.length <= maxLength) return str
    return str.substring(0, maxLength - 3) + '...'
  }

  private prepareDescription(product: any): string {
    let description = product.prod_note || ''
    
    // Добавляем дополнительную информацию
    if (product.prod_uses) {
      description += `\n\nПрименение: ${product.prod_uses}`
    }

    if (product.prod_composition) {
      description += `\n\nСостав: ${product.prod_composition}`
    }

    return this.truncateString(description, 4000)
  }

  private parseCategoryId(categoryStr: string | number): number {
    if (typeof categoryStr === 'number') return categoryStr
    if (typeof categoryStr === 'string') {
      const parsed = parseInt(categoryStr)
      return isNaN(parsed) ? 0 : parsed
    }
    return 0
  }

  private parseWeight(weight: string | number): number {
    if (typeof weight === 'number') return weight
    if (typeof weight === 'string') {
      const parsed = parseFloat(weight.replace(/[^\d.,]/g, '').replace(',', '.'))
      return isNaN(parsed) ? 0 : Math.round(parsed * 1000) // Конвертируем в граммы
    }
    return 0
  }
}

// Экспортируем singleton instance
export const ozonService = new OzonService()

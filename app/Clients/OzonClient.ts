import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import loadSettings from 'App/Helpers/loadSettings'
import { 
  IOzonClient, 
  IOzonRateLimiter, 
  IOzonErrorHandler 
} from 'App/Interfaces/ozon/OzonInterfaces'
import {
  OzonApiResponse,
  OzonSettings,
  OzonProductImportRequest,
  OzonProductInfoRequest,
  OzonStockInfoRequest,
  OzonStockUpdate,
  OzonPriceUpdate,
  OzonImageUpload,
  OzonImageUploadResponse,
  OzonCategoryTree,
  OzonProductInfo
} from 'App/Interfaces/ozon/OzonTypes'

// Простой rate limiter
class SimpleRateLimiter implements IOzonRateLimiter {
  private requests: number[] = []
  private readonly maxRequests = 100 // 100 запросов
  private readonly timeWindow = 60000 // за минуту

  async canMakeRequest(): Promise<boolean> {
    const now = Date.now()
    this.requests = this.requests.filter(time => now - time < this.timeWindow)
    return this.requests.length < this.maxRequests
  }

  async waitForNextRequest(): Promise<void> {
    while (!(await this.canMakeRequest())) {
      await new Promise(resolve => setTimeout(resolve, 1000))
    }
  }

  recordRequest(): void {
    this.requests.push(Date.now())
  }

  getRemainingRequests(): number {
    const now = Date.now()
    this.requests = this.requests.filter(time => now - time < this.timeWindow)
    return Math.max(0, this.maxRequests - this.requests.length)
  }

  getResetTime(): Date {
    if (this.requests.length === 0) return new Date()
    return new Date(this.requests[0] + this.timeWindow)
  }
}

// Обработчик ошибок
class OzonErrorHandler implements IOzonErrorHandler {
  async handleApiError(error: any, context: string): Promise<{
    shouldRetry: boolean
    retryAfter?: number
    errorMessage: string
  }> {
    const errorMessage = this.getErrorMessage(error)
    console.error(`Ozon API Error in ${context}:`, errorMessage)

    // Проверяем статус код для определения стратегии retry
    if (error.response?.status === 429) {
      return {
        shouldRetry: true,
        retryAfter: 60000, // 1 минута
        errorMessage: 'Rate limit exceeded'
      }
    }

    if (error.response?.status >= 500) {
      return {
        shouldRetry: true,
        retryAfter: 5000, // 5 секунд
        errorMessage: 'Server error'
      }
    }

    if (error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT') {
      return {
        shouldRetry: true,
        retryAfter: 3000, // 3 секунды
        errorMessage: 'Connection error'
      }
    }

    return {
      shouldRetry: false,
      errorMessage
    }
  }

  isRetryableError(error: any): boolean {
    const status = error.response?.status
    return status === 429 || status >= 500 || 
           error.code === 'ECONNRESET' || 
           error.code === 'ETIMEDOUT'
  }

  getErrorMessage(error: any): string {
    if (error.response?.data?.error?.message) {
      return error.response.data.error.message
    }
    if (error.response?.data?.message) {
      return error.response.data.message
    }
    if (error.message) {
      return error.message
    }
    return 'Unknown error'
  }
}

export class OzonClient implements IOzonClient {
  private axiosInstance: AxiosInstance
  private rateLimiter: IOzonRateLimiter
  private errorHandler: IOzonErrorHandler
  private settings: OzonSettings | null = null

  constructor() {
    this.rateLimiter = new SimpleRateLimiter()
    this.errorHandler = new OzonErrorHandler()
    
    // Создаем базовый axios instance
    this.axiosInstance = axios.create({
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    })

    // Добавляем interceptor для автоматического добавления заголовков
    this.axiosInstance.interceptors.request.use(async (config) => {
      await this.ensureSettings()
      
      if (this.settings) {
        config.baseURL = this.settings.ozon_api_url
        config.headers['Client-Id'] = this.settings.ozon_client_id
        config.headers['Api-Key'] = this.settings.ozon_api_key
      }

      return config
    })
  }

  // Загрузка настроек через loadSettings
  private async ensureSettings(): Promise<void> {
    if (!this.settings) {
      this.settings = await loadSettings([
        'ozon_api_key',
        'ozon_client_id', 
        'ozon_api_url',
        'ozon_warehouse_id',
        'ozon_sync_enabled',
        'ozon_auto_sync_interval',
        'ozon_default_category_id',
        'ozon_image_upload_enabled',
        'ozon_image_base_url',
        'ozon_max_images_per_product'
      ])

      // Устанавливаем значения по умолчанию
      this.settings = {
        ozon_api_url: 'https://api-seller.ozon.ru',
        ozon_warehouse_id: 0,
        ozon_sync_enabled: false,
        ozon_auto_sync_interval: 60,
        ozon_default_category_id: 0,
        ozon_image_upload_enabled: false,
        ozon_image_base_url: '',
        ozon_max_images_per_product: 10,
        ...this.settings
      }
    }
  }

  // Обновление настроек (сброс кэша)
  public resetSettings(): void {
    this.settings = null
  }

  // Базовые HTTP методы с retry логикой
  async get<T = any>(endpoint: string, params?: any): Promise<OzonApiResponse<T>> {
    return this.makeRequest('GET', endpoint, undefined, params)
  }

  async post<T = any>(endpoint: string, data?: any): Promise<OzonApiResponse<T>> {
    return this.makeRequest('POST', endpoint, data)
  }

  private async makeRequest<T = any>(
    method: 'GET' | 'POST',
    endpoint: string,
    data?: any,
    params?: any,
    retryCount = 0
  ): Promise<OzonApiResponse<T>> {
    const maxRetries = 3

    try {
      // Проверяем rate limit
      await this.rateLimiter.waitForNextRequest()
      this.rateLimiter.recordRequest()

      const config: AxiosRequestConfig = {
        method,
        url: endpoint,
        data,
        params
      }

      const response: AxiosResponse<OzonApiResponse<T>> = await this.axiosInstance.request(config)
      return response.data

    } catch (error) {
      const errorInfo = await this.errorHandler.handleApiError(error, `${method} ${endpoint}`)
      
      if (errorInfo.shouldRetry && retryCount < maxRetries) {
        if (errorInfo.retryAfter) {
          await new Promise(resolve => setTimeout(resolve, errorInfo.retryAfter))
        }
        return this.makeRequest(method, endpoint, data, params, retryCount + 1)
      }

      throw new Error(`Ozon API Error: ${errorInfo.errorMessage}`)
    }
  }

  // Методы для работы с товарами
  async importProducts(request: OzonProductImportRequest): Promise<OzonApiResponse<any>> {
    return this.post('/v2/product/import', request)
  }

  async getProductInfo(request: OzonProductInfoRequest): Promise<OzonApiResponse<{ items: OzonProductInfo[] }>> {
    return this.post('/v2/product/info', request)
  }

  // Методы для работы с остатками
  async updateStocks(request: OzonStockUpdate): Promise<OzonApiResponse<any>> {
    return this.post('/v1/product/info/stocks', request)
  }

  async getStocksInfo(request: OzonStockInfoRequest): Promise<OzonApiResponse<any>> {
    return this.post('/v2/products/stocks', request)
  }

  // Методы для работы с ценами
  async updatePrices(request: OzonPriceUpdate): Promise<OzonApiResponse<any>> {
    return this.post('/v1/product/info/prices', request)
  }

  // Методы для работы с изображениями
  async uploadImages(images: OzonImageUpload[]): Promise<OzonApiResponse<OzonImageUploadResponse>> {
    const fs = require('fs')
    const FormData = require('form-data')

    const formData = new FormData()

    for (const image of images) {
      if (image.url && image.url.startsWith('http')) {
        // Если передан HTTP URL, скачиваем изображение
        const response = await axios.get(image.url, { responseType: 'stream' })
        formData.append('images', response.data, image.file_name)
      } else if (image.url) {
        // Если передан локальный путь к файлу
        const fileStream = fs.createReadStream(image.url)
        formData.append('images', fileStream, image.file_name)
      }
    }

    // Для FormData нужно использовать специальный запрос
    return this.uploadFormData('/v1/product/pictures/upload', formData)
  }

  // Специальный метод для загрузки FormData
  private async uploadFormData<T = any>(endpoint: string, formData: any): Promise<OzonApiResponse<T>> {
    const maxRetries = 3
    let retryCount = 0

    while (retryCount <= maxRetries) {
      try {
        // Проверяем rate limit
        await this.rateLimiter.waitForNextRequest()
        this.rateLimiter.recordRequest()

        await this.ensureSettings()

        const config = {
          method: 'POST',
          url: `${this.settings!.ozon_api_url}${endpoint}`,
          data: formData,
          headers: {
            'Client-Id': this.settings!.ozon_client_id,
            'Api-Key': this.settings!.ozon_api_key,
            ...formData.getHeaders()
          },
          timeout: 60000 // Увеличиваем timeout для загрузки файлов
        }

        const response = await axios.request(config)
        return response.data

      } catch (error) {
        const errorInfo = await this.errorHandler.handleApiError(error, `POST ${endpoint}`)

        if (errorInfo.shouldRetry && retryCount < maxRetries) {
          retryCount++
          if (errorInfo.retryAfter) {
            await new Promise(resolve => setTimeout(resolve, errorInfo.retryAfter))
          }
          continue
        }

        throw new Error(`Ozon API Error: ${errorInfo.errorMessage}`)
      }
    }

    throw new Error('Max retries exceeded')
  }

  async getImagesInfo(fileNames: string[]): Promise<OzonApiResponse<any>> {
    return this.post('/v1/product/pictures/info', { file_names: fileNames })
  }

  // Методы для работы с категориями
  async getCategoryTree(categoryId?: number, language = 'DEFAULT'): Promise<OzonApiResponse<OzonCategoryTree>> {
    const data: any = { language }
    if (categoryId) {
      data.category_id = categoryId
    }
    return this.post('/v2/category/tree', data)
  }

  async getCategoryAttributes(categoryId: number, language = 'DEFAULT'): Promise<OzonApiResponse<any>> {
    return this.post('/v3/categories/attributes', {
      category_id: categoryId,
      language
    })
  }

  // Получение текущих настроек
  async getSettings(): Promise<OzonSettings> {
    await this.ensureSettings()
    return this.settings!
  }

  // Проверка доступности API
  async healthCheck(): Promise<boolean> {
    try {
      await this.getCategoryTree()
      return true
    } catch (error) {
      console.error('Ozon API health check failed:', error)
      return false
    }
  }
}
